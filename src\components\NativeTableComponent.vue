<template>
  <div class="native-table-component">
    <!-- 筛选面板 -->
    <div v-if="showFilter" class="filter-panel">
      <div class="filter-conditions">
        <div v-for="(condition, index) in filterConditions" :key="index" class="filter-row">
          <div class="filter-item">
            <select v-model="condition.column">
              <option value="">所有列</option>
              <option
                v-for="(col, colIndex) in columns"
                :key="colIndex"
                :value="colIndex.toString()"
              >
                {{ col.title }}
              </option>
            </select>
          </div>
          <div class="filter-item">
            <select v-model="condition.operator">
              <option value="contains">包含</option>
              <option value="equals">等于</option>
              <option value="starts_with">开头是</option>
              <option value="ends_with">结尾是</option>
              <option value="greater_than">大于</option>
              <option value="less_than">小于</option>
            </select>
          </div>
          <div class="filter-item">
            <input v-model="condition.value" placeholder="输入关键词" @keyup.enter="doFilter" />
          </div>
          <button v-if="filterConditions.length > 1" class="action-btn remove-condition-btn" @click="removeFilterCondition(index)" title="移除此条件">
            ➖
          </button>
        </div>
        <div class="filter-actions">
          <button class="action-btn add-condition-btn" @click="addFilterCondition" title="添加筛选条件">
            ➕ 添加条件
          </button>
          <div class="filter-item logical-operator">
            <label for="logical-operator">条件关系:</label>
            <select id="logical-operator" v-model="logicalOperator">
              <option value="AND">AND (所有条件都满足)</option>
              <option value="OR">OR (任一条件满足)</option>
            </select>
          </div>
          <button class="primary-btn" @click="doFilter">筛选</button>
          <button class="default-btn" @click="resetFilter">重置</button>
        </div>
      </div>
      <!-- 功能按钮区域 -->
      <div class="action-section" v-if="editable || enableCopyPaste">
        <button v-if="enableCopyPaste" class="action-btn" @click="handleCopy" title="复制选中内容">
          📋 复制
        </button>
        <button v-if="enableCopyPaste" class="action-btn" @click="handlePaste" title="粘贴内容">
          📄 粘贴
        </button>
        <button v-if="editable" class="action-btn delete-btn" @click="handleDeleteRows" title="删除选中行">
          🗑️ 删除行
        </button>
        <button v-if="editable" class="action-btn add-btn" @click="handleAddRow" title="添加新行">
          ➕ 添加行
        </button>
        <button v-if="autoWidth" class="action-btn" @click="handleAutoFitColumns" title="自动调整所有列宽">
          📏 自适应列宽
        </button>
        <input
          v-if="enableExcelImport"
          ref="excelFileInput"
          type="file"
          accept=".xlsx,.xls"
          @change="handleExcelImport"
          style="display: none"
        />
        <button
          v-if="enableExcelImport"
          class="action-btn import-btn"
          @click="triggerExcelImport"
          title="导入Excel文件"
        >
          📥 导入Excel
        </button>
        <button class="action-btn export-btn" @click="handleExportExcel" title="导出Excel文件">
          📊 导出Excel
        </button>
        <button class="action-btn" @click="handleExportData" title="导出CSV数据">
          💾 导出CSV
        </button>
        <button
          v-if="enablePushUpdate"
          class="action-btn push-btn"
          @click="handlePushUpdate"
          :disabled="pushLoading"
          title="推送数据到后台更新"
        >
          {{ pushLoading ? '⏳ 推送中...' : '🔄 推送更新' }}
        </button>
      </div>
    </div>
    
    <!-- 原生表格容器 -->
    <div 
      ref="tableContainer" 
      :style="{ width: width + 'px', height: height + 'px' }"
      class="table-container"
      @click="handleTableClick"
      @dblclick="handleTableDoubleClick"
      @keydown="handleKeyDown"
      tabindex="0"
    >
      <table ref="tableRef" class="native-table">
        <thead>
          <tr>
            <th 
              v-for="(col, colIndex) in columns" 
              :key="colIndex"
              :style="{ width: col.width + 'px', minWidth: col.minWidth + 'px' }"
              class="table-header"
              @mousedown="startColumnResize($event, colIndex)"
            >
              <div class="header-content">
                {{ col.title }}
                <div class="resize-handle"></div>
              </div>
            </th>
          </tr>
        </thead>
        <tbody>
          <tr 
            v-for="(record, rowIndex) in displayRecords" 
            :key="rowIndex"
            :class="{ 
              'selected-row': isRowSelected(rowIndex),
              'editing-row': editingCell.row === rowIndex 
            }"
            class="table-row"
          >
            <td 
              v-for="(col, colIndex) in columns" 
              :key="colIndex"
              :class="{ 
                'selected-cell': isCellSelected(rowIndex, colIndex),
                'editing-cell': editingCell.row === rowIndex && editingCell.col === colIndex 
              }"
              class="table-cell"
              :data-row="rowIndex"
              :data-col="colIndex"
            >
              <div v-if="editingCell.row === rowIndex && editingCell.col === colIndex" class="cell-editor">
                <input
                  ref="cellInput"
                  v-model="editingValue"
                  @blur="finishEdit"
                  @keydown="handleEditKeyDown"
                  class="cell-input"
                  type="text"
                />
              </div>
              <div v-else class="cell-content">
                {{ getCellValue(record, col.field) }}
              </div>
            </td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, watch, nextTick, onUnmounted, computed } from 'vue'
import * as ExcelJS from 'exceljs'

// Props定义
const props = defineProps({
  // 二维数组数据，第一行为标题
  data: {
    type: Array,
    required: true,
    default: () => []
  },
  // 表格宽度
  width: {
    type: Number,
    default: 600
  },
  // 表格高度
  height: {
    type: Number,
    default: 300
  },
  // 是否显示筛选面板
  showFilter: {
    type: Boolean,
    default: true
  },
  // 额外的表格配置
  tableOptions: {
    type: Object,
    default: () => ({})
  },
  // 是否启用编辑功能
  editable: {
    type: Boolean,
    default: true
  },
  // 是否启用复制粘贴
  enableCopyPaste: {
    type: Boolean,
    default: true
  },
  // 是否自动调整列宽
  autoWidth: {
    type: Boolean,
    default: true
  },
  // 是否启用Excel导入功能
  enableExcelImport: {
    type: Boolean,
    default: false
  },
  // 是否启用后台推送更新功能
  enablePushUpdate: {
    type: Boolean,
    default: false
  },
  // 后台推送更新的API端点
  pushUpdateEndpoint: {
    type: String,
    default: ''
  },
  // 表名（用于后台推送）
  tableName: {
    type: String,
    default: ''
  }
})

// 响应式数据
const tableRef = ref()
const tableContainer = ref()
const cellInput = ref()
const excelFileInput = ref()
const filterConditions = ref([{ column: '', operator: 'contains', value: '' }])
const logicalOperator = ref('AND') // 'AND' or 'OR'
const pushLoading = ref(false)

const columns = ref([])
const records = ref([])
const originalRecords = ref([])

// 表格状态
const selectedCells = ref(new Set())
const selectedRows = ref(new Set())
const editingCell = ref({ row: -1, col: -1 })
const editingValue = ref('')

// 列宽调整相关
const isResizing = ref(false)
const resizingColumn = ref(-1)
const startX = ref(0)
const startWidth = ref(0)

// 计算属性：显示的记录（经过筛选）
const displayRecords = computed(() => {
  return records.value
})

// 定义事件发射
const emit = defineEmits(['data-change', 'cell-edit', 'copy', 'paste', 'cell-select'])

// 发射数据变化事件
function emitDataChange() {
  // 将表格数据转换回二维数组格式
  const headers = columns.value.map(col => col.title);
  const dataRows = records.value.map(record =>
    columns.value.map(col => record[col.field])
  );
  const newData = [headers, ...dataRows];

  emit('data-change', newData);
}

// 安全的数据变化事件发射器（带内部更新标记）
let isInternalUpdate = false;
function safeEmitDataChange() {
  isInternalUpdate = true;
  emitDataChange();
  nextTick(() => {
    isInternalUpdate = false;
  });
}

// 防抖的数据变化事件发射器
let debounceTimer = null;
function debouncedEmitDataChange() {
  if (debounceTimer) {
    clearTimeout(debounceTimer);
  }
  debounceTimer = setTimeout(() => {
    safeEmitDataChange();
    debounceTimer = null;
  }, 100); // 100ms 防抖延迟
}

// 原生表格的基础方法
const getCellValue = (record, field) => {
  return record[field] || ''
}

const isCellSelected = (row, col) => {
  return selectedCells.value.has(`${row}-${col}`)
}

const isRowSelected = (row) => {
  return selectedRows.value.has(row)
}

// 表格事件处理
const handleTableClick = (event) => {
  const cell = event.target.closest('td')
  if (!cell) return

  const row = parseInt(cell.dataset.row)
  const col = parseInt(cell.dataset.col)

  if (!isNaN(row) && !isNaN(col)) {
    selectCell(row, col, event.ctrlKey)
  }
}

const handleTableDoubleClick = (event) => {
  if (!props.editable) return

  const cell = event.target.closest('td')
  if (!cell) return

  const row = parseInt(cell.dataset.row)
  const col = parseInt(cell.dataset.col)

  if (!isNaN(row) && !isNaN(col)) {
    startEdit(row, col)
  }
}

const selectCell = (row, col, multiSelect = false) => {
  if (!multiSelect) {
    selectedCells.value.clear()
    selectedRows.value.clear()
  }

  selectedCells.value.add(`${row}-${col}`)
  selectedRows.value.add(row)

  // 触发选择事件
  emit('cell-select', { row, col })
}

const startEdit = (row, col) => {
  if (!props.editable) return

  editingCell.value = { row, col }
  const field = columns.value[col]?.field
  editingValue.value = records.value[row] ? records.value[row][field] || '' : ''

  nextTick(() => {
    if (cellInput.value && cellInput.value[0]) {
      cellInput.value[0].focus()
      cellInput.value[0].select()
    }
  })
}

const finishEdit = () => {
  if (editingCell.value.row === -1) return

  const { row, col } = editingCell.value
  const field = columns.value[col]?.field

  if (field && records.value[row]) {
    const oldValue = records.value[row][field]
    const newValue = editingValue.value

    if (oldValue !== newValue) {
      records.value[row][field] = newValue

      // 同步更新原始记录
      if (originalRecords.value[row]) {
        originalRecords.value[row][field] = newValue
      }

      // 触发数据变化事件
      safeEmitDataChange()
      emit('cell-edit', { col, row, newValue, oldValue })
    }
  }

  editingCell.value = { row: -1, col: -1 }
  editingValue.value = ''
}

const handleEditKeyDown = (event) => {
  if (event.key === 'Enter') {
    event.preventDefault()
    finishEdit()
  } else if (event.key === 'Escape') {
    event.preventDefault()
    editingCell.value = { row: -1, col: -1 }
    editingValue.value = ''
  }
}

const handleKeyDown = (event) => {
  if (!props.enableCopyPaste) return

  if (event.ctrlKey) {
    if (event.key === 'c') {
      event.preventDefault()
      handleCopy()
    } else if (event.key === 'v') {
      event.preventDefault()
      handlePaste()
    }
  }
}

// 列宽调整功能
const startColumnResize = (event, colIndex) => {
  event.preventDefault()
  isResizing.value = true
  resizingColumn.value = colIndex
  startX.value = event.clientX
  startWidth.value = columns.value[colIndex].width || 120

  document.addEventListener('mousemove', handleColumnResize)
  document.addEventListener('mouseup', stopColumnResize)
}

const handleColumnResize = (event) => {
  if (!isResizing.value) return

  const deltaX = event.clientX - startX.value
  const newWidth = Math.max(60, startWidth.value + deltaX)

  if (columns.value[resizingColumn.value]) {
    columns.value[resizingColumn.value].width = newWidth
  }
}

const stopColumnResize = () => {
  isResizing.value = false
  resizingColumn.value = -1
  document.removeEventListener('mousemove', handleColumnResize)
  document.removeEventListener('mouseup', stopColumnResize)
}

// 从二维数组生成列配置和记录
function generateTableData(data) {
  if (!data || data.length === 0) {
    columns.value = []
    records.value = []
    originalRecords.value = []
    return
  }

  // 第一行作为标题
  const headers = data[0]
  const dataRows = data.slice(1)

  // 智能检测列数据类型
  const detectColumnType = (columnIndex) => {
    let isNumeric = true;
    const uniqueValues = new Set();

    for (let i = 0; i < Math.min(dataRows.length, 20); i++) {
      const cellValue = dataRows[i][columnIndex];
      const strValue = String(cellValue || '').trim();

      if (strValue) {
        uniqueValues.add(strValue);

        // 检查是否是数字
        if (isNumeric && isNaN(Number(cellValue)) && cellValue !== null && cellValue !== undefined) {
          isNumeric = false;
        }
      }
    }

    return {
      isNumeric,
      uniqueValues: Array.from(uniqueValues)
    };
  };

  // 智能计算列宽度
  const calculateColumnWidth = (columnIndex) => {
    if (!props.autoWidth) {
      return 120; // 默认宽度
    }

    const title = headers[columnIndex] || '';
    const titleLength = title.toString().length;

    let maxContentLength = 0;
    for (let i = 0; i < Math.min(dataRows.length, 10); i++) {
      const cellValue = dataRows[i][columnIndex];
      const strValue = String(cellValue || '');
      maxContentLength = Math.max(maxContentLength, strValue.length);
    }

    const { isNumeric } = detectColumnType(columnIndex);

    // 根据类型和长度设置宽度
    if (isNumeric) {
      return Math.max(80, Math.min(maxContentLength * 8 + 20, 150));
    } else {
      const baseWidth = Math.max(titleLength, maxContentLength) * 8 + 20;
      return Math.min(Math.max(100, baseWidth), 250);
    }
  };

  // 生成列配置
  columns.value = headers.map((title, index) => {
    const width = calculateColumnWidth(index);
    const columnType = detectColumnType(index);

    const columnConfig = {
      field: index.toString(),
      title: title,
      width: width,
      minWidth: 60,
      maxWidth: 300,
      type: columnType.isNumeric ? 'number' : 'text',
      align: columnType.isNumeric ? 'right' : 'left'
    };

    return columnConfig;
  });

  // 生成记录数据
  const recordsData = dataRows.map(row => {
    const record = {}
    row.forEach((cell, index) => {
      record[index.toString()] = cell
    })
    return record
  })

  records.value = [...recordsData]
  originalRecords.value = [...recordsData]
}

// 初始化原生表格
function initTable() {
  if (!tableContainer.value || columns.value.length === 0) return

  // 原生表格不需要特殊初始化，数据绑定由Vue处理
  console.log('原生表格初始化完成:', {
    columns: columns.value.length,
    records: records.value.length,
    editable: props.editable
  });

  // 确保表格容器可以获得焦点
  if (tableContainer.value) {
    tableContainer.value.focus()
  }
}

// 处理复制操作
const handleCopy = async () => {
  if (!props.enableCopyPaste) return

  try {
    const selectedData = getSelectedData()
    if (selectedData.length === 0) {
      console.warn('没有选中的数据可复制')
      return
    }

    // 将数据转换为制表符分隔的文本
    const textData = selectedData.map(row => row.join('\t')).join('\n')

    // 写入剪贴板
    if (navigator.clipboard && navigator.clipboard.writeText) {
      await navigator.clipboard.writeText(textData)
      console.log('复制成功')
      emit('copy', { data: selectedData })
    } else {
      // 降级方案：使用传统的复制方法
      const textArea = document.createElement('textarea')
      textArea.value = textData
      document.body.appendChild(textArea)
      textArea.select()
      document.execCommand('copy')
      document.body.removeChild(textArea)
      console.log('复制成功（降级方案）')
      emit('copy', { data: selectedData })
    }
  } catch (error) {
    console.error('复制失败:', error)
  }
}

// 获取选中的数据
const getSelectedData = () => {
  const selectedData = []

  // 如果没有选中的单元格，返回空数组
  if (selectedCells.value.size === 0) {
    return selectedData
  }

  // 获取选中的行和列范围
  const selectedPositions = Array.from(selectedCells.value).map(cellKey => {
    const [row, col] = cellKey.split('-').map(Number)
    return { row, col }
  })

  if (selectedPositions.length === 0) return selectedData

  // 计算选中区域的边界
  const minRow = Math.min(...selectedPositions.map(p => p.row))
  const maxRow = Math.max(...selectedPositions.map(p => p.row))
  const minCol = Math.min(...selectedPositions.map(p => p.col))
  const maxCol = Math.max(...selectedPositions.map(p => p.col))

  // 构建选中区域的数据
  for (let row = minRow; row <= maxRow; row++) {
    const rowData = []
    for (let col = minCol; col <= maxCol; col++) {
      const field = columns.value[col]?.field
      const value = records.value[row] && field ? records.value[row][field] || '' : ''
      rowData.push(value)
    }
    selectedData.push(rowData)
  }

  return selectedData
}

// 处理粘贴操作
const handlePaste = async () => {
  console.log('=== 开始粘贴操作 ===');

  if (!props.enableCopyPaste) {
    console.log('❌ 粘贴功能未启用');
    return;
  }

  try {
    // 获取当前选中的单元格
    if (selectedCells.value.size === 0) {
      console.warn('❌ 没有选中的单元格，无法粘贴');
      return;
    }

    // 尝试从剪贴板读取数据
    if (!navigator.clipboard || !navigator.clipboard.readText) {
      console.warn('⚠️ 浏览器不支持剪贴板API');
      return;
    }

    const clipboardText = await navigator.clipboard.readText();
    if (!clipboardText) {
      console.warn('❌ 剪贴板为空');
      return;
    }

    console.log('📋 剪贴板内容:', `"${clipboardText}"`);

    // 解析剪贴板数据
    const rows = clipboardText.split(/\r\n|\n|\r/).filter(row => row !== '');
    const pasteData = rows.map(row => row.split('\t'));

    console.log('📊 解析后的粘贴数据:', pasteData);

    // 获取起始粘贴位置（第一个选中的单元格）
    const firstSelectedCell = Array.from(selectedCells.value)[0];
    const [startRow, startCol] = firstSelectedCell.split('-').map(Number);

    console.log('📍 起始粘贴位置: 行', startRow, '列', startCol);

    // 更新数据
    let hasChanges = false;
    for (let i = 0; i < pasteData.length; i++) {
      const targetRow = startRow + i;
      if (targetRow >= records.value.length || targetRow < 0) {
        console.log(`⚠️ 目标行${targetRow}超出范围，跳过`);
        break;
      }

      for (let j = 0; j < pasteData[i].length; j++) {
        const targetCol = startCol + j;
        if (targetCol >= columns.value.length) {
          console.log(`⚠️ 目标列${targetCol}超出范围，跳过`);
          break;
        }

        const field = columns.value[targetCol]?.field;
        if (field && records.value[targetRow]) {
          const newValue = pasteData[i][j];
          const oldValue = records.value[targetRow][field];
          if (oldValue !== newValue) {
            console.log(`🔄 更新 行${targetRow} 列${targetCol} 字段"${field}": "${oldValue}" -> "${newValue}"`);
            records.value[targetRow][field] = newValue;

            // 同步更新原始记录
            if (originalRecords.value[targetRow]) {
              originalRecords.value[targetRow][field] = newValue;
            }

            hasChanges = true;
          }
        }
      }
    }

    if (hasChanges) {
      safeEmitDataChange();
      emit('paste', { data: pasteData, startRow, startCol });
      console.log('✅ 粘贴完成');
    } else {
      console.log('ℹ️ 没有数据变化');
    }

  } catch (error) {
    console.error('❌ 粘贴操作失败:', error);
  }

  console.log('=== 粘贴操作结束 ===');
}

// 处理自动调整列宽
const handleAutoFitColumns = () => {
  try {
    columns.value.forEach((col) => {
      // 计算列内容的最大宽度
      let maxWidth = col.title.length * 8 + 40; // 标题宽度

      records.value.forEach(record => {
        const cellValue = String(record[col.field] || '');
        const cellWidth = cellValue.length * 8 + 20;
        maxWidth = Math.max(maxWidth, cellWidth);
      });

      // 限制最大和最小宽度
      col.width = Math.min(Math.max(maxWidth, 80), 300);
    });

    console.log('自动调整列宽完成');
  } catch (error) {
    console.error('自动调整列宽失败:', error);
  }
}

// 处理删除行
const handleDeleteRows = () => {
  try {
    if (selectedRows.value.size === 0) {
      alert('请先选择要删除的行');
      return;
    }

    const rowsToDelete = Array.from(selectedRows.value).sort((a, b) => b - a); // 从后往前删除

    if (!confirm(`确定要删除选中的 ${rowsToDelete.length} 行吗？`)) {
      return;
    }

    // 从后往前删除行，避免索引变化问题
    rowsToDelete.forEach(rowIndex => {
      if (rowIndex >= 0 && rowIndex < records.value.length) {
        records.value.splice(rowIndex, 1);
      }
      if (rowIndex >= 0 && rowIndex < originalRecords.value.length) {
        originalRecords.value.splice(rowIndex, 1);
      }
    });

    // 清空选择
    selectedCells.value.clear();
    selectedRows.value.clear();

    safeEmitDataChange();
    console.log(`成功删除 ${rowsToDelete.length} 行`);

  } catch (error) {
    console.error('删除行失败:', error);
    alert('删除行失败，请重试');
  }
};

// 处理添加行 (支持自定义行数)
const handleAddRow = () => {
  if (columns.value.length === 0) return;

  // 1. 让用户输入要添加的行数
  const rowCountStr = prompt('请输入要添加的行数', '1');
  if (rowCountStr === null) {
    // 用户点击了取消
    return;
  }
  const rowCount = parseInt(rowCountStr, 10);

  if (isNaN(rowCount) || rowCount <= 0) {
    alert('请输入有效的正整数行数');
    return;
  }

  try {
    // 2. 创建并添加对应数量的新行
    for (let i = 0; i < rowCount; i++) {
      const newRow = {};
      columns.value.forEach(col => {
        newRow[col.field] = '';
      });
      records.value.push(newRow);
      originalRecords.value.push({ ...newRow });
    }

    // 3. 触发数据变化事件
    safeEmitDataChange();

    // 4. 选中首个新行
    const firstNewRowIndex = records.value.length - rowCount;
    selectedCells.value.clear();
    selectedRows.value.clear();
    selectCell(firstNewRowIndex, 0);

    console.log(`成功添加 ${rowCount} 行`);
  } catch (error) {
    console.error('添加行失败:', error);
    alert('添加行失败，请重试');
  }
};

// Helper function to check if a record satisfies a single condition
function checkCondition(record, condition) {
  const { column, operator, value } = condition;
  const filterValue = String(value || '').trim().toLowerCase();

  if (!filterValue) {
    return true; // Empty filter value means this condition is always true
  }

  // If no specific column is selected, check all columns
  const columnsToCheck = column === '' ? Object.keys(record) : [column];

  for (const colField of columnsToCheck) {
    const cellValue = String(record[colField] || '').toLowerCase();

    switch (operator) {
      case 'contains':
        if (cellValue.includes(filterValue)) return true;
        break;
      case 'equals':
        if (cellValue === filterValue) return true;
        break;
      case 'starts_with':
        if (cellValue.startsWith(filterValue)) return true;
        break;
      case 'ends_with':
        if (cellValue.endsWith(filterValue)) return true;
        break;
      case 'greater_than':
        // Attempt numeric comparison
        const numCellValue = parseFloat(cellValue);
        const numFilterValue = parseFloat(filterValue);
        if (!isNaN(numCellValue) && !isNaN(numFilterValue) && numCellValue > numFilterValue) return true;
        break;
      case 'less_than':
        // Attempt numeric comparison
        const numCellValueLt = parseFloat(cellValue);
        const numFilterValueLt = parseFloat(filterValue);
        if (!isNaN(numCellValueLt) && !isNaN(numFilterValueLt) && numCellValueLt < numFilterValueLt) return true;
        break;
      default:
        // Fallback for unknown operators, treat as contains
        if (cellValue.includes(filterValue)) return true;
        break;
    }
  }
  return false; // No match found for this record with this condition
}

// 筛选功能
function doFilter() {
  const activeConditions = filterConditions.value.filter(c => c.value.trim() !== '');

  if (activeConditions.length === 0) {
    records.value = [...originalRecords.value]
  } else {
    records.value = originalRecords.value.filter(row => {
      if (logicalOperator.value === 'AND') {
        // All conditions must be true
        return activeConditions.every(condition => checkCondition(row, condition));
      } else { // OR
        // At least one condition must be true
        return activeConditions.some(condition => checkCondition(row, condition));
      }
    })
  }
}

// 重置筛选
function resetFilter() {
  filterConditions.value = [{ column: '', operator: 'contains', value: '' }];
  logicalOperator.value = 'AND';
  records.value = [...originalRecords.value]
}

// Add a new filter condition row
function addFilterCondition() {
  filterConditions.value.push({ column: '', operator: 'contains', value: '' });
}

// Remove a filter condition row
function removeFilterCondition(index) {
  if (filterConditions.value.length > 1) {
    filterConditions.value.splice(index, 1);
  }
}

// 触发Excel导入文件选择
const triggerExcelImport = () => {
  if (excelFileInput.value) {
    excelFileInput.value.click()
  }
}

// 显示导入模式选择对话框
const showImportModeDialog = () => {
  return new Promise((resolve) => {
    const dialog = document.createElement('div')
    dialog.style.cssText = `
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(0,0,0,0.5);
      display: flex;
      justify-content: center;
      align-items: center;
      z-index: 9999;
    `

    dialog.innerHTML = `
      <div style="background: white; padding: 24px; border-radius: 12px; min-width: 320px; box-shadow: 0 8px 32px rgba(0,0,0,0.2);">
        <h3 style="margin: 0 0 16px 0; color: #333; font-size: 18px;">选择导入模式</h3>
        <div style="margin: 16px 0;">
          <label style="display: flex; align-items: center; margin-bottom: 12px; cursor: pointer;">
            <input type="radio" name="importMode" value="append" checked style="margin-right: 8px;">
            <span style="color: #333;">追加导入 - 在现有数据后添加新数据</span>
          </label>
          <label style="display: flex; align-items: center; cursor: pointer;">
            <input type="radio" name="importMode" value="overwrite" style="margin-right: 8px;">
            <span style="color: #333;">覆盖导入 - 替换所有现有数据</span>
          </label>
        </div>
        <div style="text-align: right; margin-top: 24px;">
          <button id="cancelImport" style="margin-right: 12px; padding: 8px 16px; border: 1px solid #ddd; background: white; border-radius: 6px; cursor: pointer;">取消</button>
          <button id="confirmImport" style="padding: 8px 16px; background: #667eea; color: white; border: none; border-radius: 6px; cursor: pointer;">
            确定
          </button>
        </div>
      </div>
    `

    document.body.appendChild(dialog)

    const cancelButton = dialog.querySelector('#cancelImport')
    const confirmButton = dialog.querySelector('#confirmImport')

    cancelButton.onclick = () => {
      document.body.removeChild(dialog)
      resolve(null)
    }

    confirmButton.onclick = () => {
      const selectedMode = dialog.querySelector('input[name="importMode"]:checked').value
      document.body.removeChild(dialog)
      resolve(selectedMode)
    }
  })
}

// 处理Excel导入
const handleExcelImport = async (event) => {
  const file = event.target.files[0]
  if (!file) return

  try {
    // 显示导入模式选择对话框
    const importMode = await showImportModeDialog()
    if (!importMode) {
      // 用户取消了导入
      event.target.value = ''
      return
    }

    const workbook = new ExcelJS.Workbook()
    await workbook.xlsx.load(file)

    const worksheet = workbook.getWorksheet(1)
    const importedData = []

    worksheet.eachRow((row) => {
      const rowData = []
      row.eachCell({ includeEmpty: true }, (cell) => {
        rowData.push(cell.value ? cell.value.toString() : '')
      })
      importedData.push(rowData)
    })

    if (importedData.length > 0) {
      let finalData

      if (importMode === 'overwrite') {
        // 覆盖模式：直接使用导入的数据
        finalData = importedData
        console.log(`Excel覆盖导入成功，共导入 ${importedData.length - 1} 条记录`)
      } else {
        // 追加模式：合并现有数据和导入数据
        const currentData = props.data || []
        if (currentData.length > 0) {
          // 保留现有表头，追加数据行
          finalData = [...currentData, ...importedData.slice(1)]
        } else {
          // 如果当前没有数据，直接使用导入数据
          finalData = importedData
        }
        console.log(`Excel追加导入成功，共追加 ${importedData.length - 1} 条记录`)
      }

      // 发出数据变化事件
      emit('data-change', finalData)
    } else {
      console.warn('Excel文件为空')
      alert('Excel文件为空，无法导入')
    }
  } catch (error) {
    console.error('Excel导入失败:', error)
    alert('Excel导入失败，请检查文件格式: ' + error.message)
  } finally {
    // 清空文件输入，允许重复选择同一文件
    event.target.value = ''
  }
}

// 处理导出Excel
const handleExportExcel = async () => {
  try {
    const headers = columns.value.map(col => col.title);
    const dataRows = records.value.map(record =>
      columns.value.map(col => record[col.field] || '')
    );

    // Identify ID card columns by header keywords
    const idCardHeaderKeywords = ['身份证号', '证件号码', '公民身份号码'];
    const idColumnFlags = headers.map(header =>
      idCardHeaderKeywords.some(keyword => header.includes(keyword))
    );

    // 创建工作簿
    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet('数据表');

    // 设置表头
    const headerRow = worksheet.addRow(headers);
    headerRow.eachCell((cell) => {
      cell.font = { bold: true, color: { argb: 'FFFFFF' } };
      cell.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: '4472C4' }
      };
      cell.alignment = { horizontal: 'center', vertical: 'middle' };
      cell.border = {
        top: { style: 'thin' },
        left: { style: 'thin' },
        bottom: { style: 'thin' },
        right: { style: 'thin' }
      };
    });

    // 添加数据行
    dataRows.forEach(row => {
      const dataRow = worksheet.addRow(row);
      dataRow.eachCell({ includeEmpty: true }, (cell, colNumber) => {
        cell.alignment = { horizontal: 'left', vertical: 'middle' };
        cell.border = {
          top: { style: 'thin' },
          left: { style: 'thin' },
          bottom: { style: 'thin' },
          right: { style: 'thin' }
        };

        // 根据列类型和内容设置单元格格式
        let value = cell.value;
        const isIdColumn = idColumnFlags[colNumber - 1];
        const isLongNumeric = typeof value === 'string' && /^\d{15,}$/.test(value.trim());

        if (isIdColumn || isLongNumeric) {
          // 使用文本格式以避免被Excel转换为科学计数法或丢失精度
          cell.numFmt = '@';
          cell.value = value !== null && value !== undefined ? String(value) : '';
        } else if (typeof value === 'string' && !isNaN(value) && value.trim() !== '') {
          // 常规数字格式化
          cell.value = parseFloat(value);
          cell.numFmt = '#,##0.00';
        }
      });
    });

    // 自动调整列宽
    worksheet.columns.forEach((column, index) => {
      let maxLength = headers[index]?.length || 10;
      dataRows.forEach(row => {
        const cellValue = String(row[index] || '');
        maxLength = Math.max(maxLength, cellValue.length);
      });
      column.width = Math.min(Math.max(maxLength + 2, 10), 50);
    });

    // 生成文件并下载
    const buffer = await workbook.xlsx.writeBuffer();
    const blob = new Blob([buffer], {
      type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    });

    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', `table_data_${new Date().toISOString().slice(0, 10)}.xlsx`);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);

    console.log('导出Excel成功');
  } catch (error) {
    console.error('导出Excel失败:', error);
    alert('导出Excel失败，请重试');
  }
};

// 处理导出CSV数据
const handleExportData = () => {
  try {
    const headers = columns.value.map(col => col.title);
    const dataRows = records.value.map(record =>
      columns.value.map(col => record[col.field])
    );
    const exportData = [headers, ...dataRows];

    // 转换为CSV格式
    const csvContent = exportData.map(row =>
      row.map(cell => `"${String(cell || '').replace(/"/g, '""')}"`).join(',')
    ).join('\n');

    // 创建下载链接
    const blob = new Blob(['\uFEFF' + csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', `table_data_${new Date().toISOString().slice(0, 10)}.csv`);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    console.log('导出CSV数据成功');
  } catch (error) {
    console.error('导出CSV数据失败:', error);
  }
}

// 处理推送后台更新
const handlePushUpdate = async () => {
  if (!props.enablePushUpdate || !props.pushUpdateEndpoint) {
    console.warn('推送更新功能未启用或未配置API端点')
    return
  }

  if (records.value.length === 0) {
    console.warn('没有数据可以推送')
    return
  }

  try {
    // 确认对话框
    if (!confirm('确定要将当前数据推送到后台进行更新吗？')) {
      return
    }

    pushLoading.value = true

    // 构造推送数据
    const headers = columns.value.map(col => col.title)
    const dataRows = records.value.map(record =>
      columns.value.map(col => record[col.field] || '')
    )
    const pushData = {
      tableName: props.tableName || '数据表',
      data: [headers, ...dataRows],
      timestamp: new Date().toISOString()
    }

    // 发送推送请求
    const response = await fetch(props.pushUpdateEndpoint, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(pushData)
    })

    if (response.ok) {
      const result = await response.json()
      if (result.code === 200) {
        console.log('数据推送成功')
        alert('数据推送成功')
      } else {
        throw new Error(result.message || '推送失败')
      }
    } else {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`)
    }
  } catch (error) {
    console.error('推送失败:', error)
    alert(`推送失败: ${error.message}`)
  } finally {
    pushLoading.value = false
  }
}

// 监听数据变化
watch(() => props.data, (newData, oldData) => {
  // 如果是内部更新触发的，跳过处理
  if (isInternalUpdate) {
    console.log('跳过内部更新触发的数据变化');
    return;
  }

  // 检查是否是真正的外部数据变化
  if (oldData && JSON.stringify(newData) === JSON.stringify(oldData)) {
    console.log('数据内容未变化，跳过更新');
    return;
  }

  console.log('外部数据变化，重新生成表格');
  generateTableData(newData)
  nextTick(() => {
    initTable()
  })
}, { deep: true, immediate: true })

// 监听表格尺寸变化
watch([() => props.width, () => props.height], () => {
  // 原生表格会自动适应容器尺寸变化
  console.log('表格尺寸变化:', props.width, props.height)
})

// 组件挂载
onMounted(() => {
  nextTick(() => {
    initTable()
  })
})

// 组件卸载前清理资源
onUnmounted(() => {
  // 清理列宽调整事件监听器
  document.removeEventListener('mousemove', handleColumnResize)
  document.removeEventListener('mouseup', stopColumnResize)
})

// 暴露方法给父组件
defineExpose({
  resetFilter,
  doFilter,
  getTableInstance: () => null, // 原生表格没有实例

  // 数据操作方法
  getData: () => {
    const headers = columns.value.map(col => col.title);
    const dataRows = records.value.map(record =>
      columns.value.map(col => record[col.field])
    );
    return [headers, ...dataRows];
  },

  setData: (newData) => {
    generateTableData(newData);
  },

  // 行操作方法
  addRow: handleAddRow,
  deleteRows: handleDeleteRows,

  addRowData: (rowData) => {
    if (columns.value.length === 0) return;

    const newRow = {};
    columns.value.forEach((col, index) => {
      newRow[col.field] = rowData[index] || '';
    });

    records.value.push(newRow);
    originalRecords.value.push({ ...newRow });
    safeEmitDataChange();
  },

  deleteRowByIndex: (rowIndex) => {
    if (rowIndex >= 0 && rowIndex < records.value.length) {
      records.value.splice(rowIndex, 1);
      originalRecords.value.splice(rowIndex, 1);
      safeEmitDataChange();
    }
  },

  // 编辑相关方法
  startEdit: (row, col) => {
    if (props.editable) {
      startEdit(row, col);
    }
  },

  endEdit: () => {
    finishEdit();
  },

  // 选择相关方法
  selectCell: (row, col) => {
    selectCell(row, col);
  },

  getSelectedData: () => {
    return getSelectedData();
  },

  getSelectedRows: () => {
    return Array.from(selectedRows.value);
  },

  // 复制粘贴方法
  copy: () => {
    if (props.enableCopyPaste) {
      handleCopy();
    }
  },

  paste: () => {
    handlePaste();
  },

  // 导出方法
  exportExcel: handleExportExcel,
  exportCSV: handleExportData,

  // 布局方法
  updateScrollBar: () => {
    // 原生表格不需要手动更新滚动条
    console.log('原生表格自动处理滚动条');
  },

  resize: () => {
    // 原生表格会自动适应容器尺寸
    console.log('原生表格自动适应尺寸');
  },

  // 列宽调整
  autoFitColumnWidth: (col) => {
    if (columns.value[col]) {
      // 计算单列的最佳宽度
      let maxWidth = columns.value[col].title.length * 8 + 40;
      records.value.forEach(record => {
        const cellValue = String(record[columns.value[col].field] || '');
        const cellWidth = cellValue.length * 8 + 20;
        maxWidth = Math.max(maxWidth, cellWidth);
      });
      columns.value[col].width = Math.min(Math.max(maxWidth, 80), 300);
    }
  },

  autoFitAllColumnWidth: () => {
    handleAutoFitColumns();
  }
})
</script>

<style scoped>
.native-table-component {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.table-container {
  overflow: auto;
  position: relative;
  border: 1px solid #ebeef5;
  border-radius: 4px;
  background: white;
}

.native-table {
  width: 100%;
  border-collapse: collapse;
  font-size: 12px;
  table-layout: fixed;
}

.table-header {
  background: #f5f7fa;
  border: 1px solid #ebeef5;
  padding: 8px 12px;
  text-align: center;
  font-weight: bold;
  position: relative;
  user-select: none;
  cursor: col-resize;
}

.header-content {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.resize-handle {
  position: absolute;
  right: 0;
  top: 0;
  bottom: 0;
  width: 4px;
  cursor: col-resize;
  background: transparent;
}

.resize-handle:hover {
  background: #409eff;
}

.table-row {
  transition: background-color 0.2s;
}

.table-row:hover {
  background-color: #f5f7fa;
}

.table-row.selected-row {
  background-color: #ecf5ff;
}

.table-row.editing-row {
  background-color: #e6f7ff;
}

.table-cell {
  border: 1px solid #ebeef5;
  padding: 8px 12px;
  text-align: left;
  vertical-align: middle;
  cursor: pointer;
  position: relative;
}

.table-cell.selected-cell {
  background-color: #f0f9ff;
  border-color: #409eff;
}

.table-cell.editing-cell {
  background-color: #e6f7ff;
  border-color: #409eff;
  border-width: 2px;
}

.cell-content {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.cell-editor {
  width: 100%;
  height: 100%;
}

.cell-input {
  width: 100%;
  height: 100%;
  border: none;
  outline: none;
  background: transparent;
  font-size: inherit;
  font-family: inherit;
  padding: 0;
  margin: 0;
}

/* 筛选面板样式 */
.filter-panel {
  background: #fff;
  padding: 16px;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0,0,0,0.05);
  border: 1px solid #e4e7ed;
}

.filter-conditions {
  display: flex;
  flex-direction: column;
  gap: 10px;
  margin-bottom: 12px;
}

.filter-row {
  display: flex;
  gap: 12px;
  align-items: center;
}

.filter-actions {
  display: flex;
  gap: 12px;
  align-items: center;
  margin-top: 10px;
}

.action-section {
  display: flex;
  gap: 8px;
  align-items: center;
  padding-top: 12px;
  border-top: 1px solid #f0f0f0;
}

.filter-item {
  display: flex;
  align-items: center;
  gap: 4px;
}

.filter-item.logical-operator {
  margin-left: auto;
  gap: 8px;
}

.filter-item select,
.filter-item input {
  padding: 6px 10px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  width: 140px;
  font-size: 12px;
  transition: all 0.3s;
}

.filter-item select:focus,
.filter-item input:focus {
  border-color: #409eff;
  outline: none;
}

.primary-btn,
.default-btn {
  padding: 8px 16px;
  border-radius: 4px;
  border: none;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.3s;
}

.primary-btn {
  background: #409eff;
  color: #fff;
}

.primary-btn:hover {
  background: #66b1ff;
}

.default-btn {
  background: #f4f4f5;
  color: #606266;
}

.default-btn:hover {
  background: #e9e9eb;
}

.action-btn {
  padding: 6px 12px;
  border-radius: 4px;
  border: 1px solid #dcdfe6;
  background: #fff;
  color: #606266;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.3s;
  display: flex;
  align-items: center;
  gap: 4px;
}

.action-btn:hover {
  background: #f5f7fa;
  border-color: #c0c4cc;
  color: #409eff;
}

.action-btn:active {
  background: #e6f7ff;
  border-color: #409eff;
}

.action-btn.delete-btn {
  background: #f56c6c;
  color: #fff;
  border-color: #f56c6c;
}

.action-btn.delete-btn:hover {
  background: #f78989;
  border-color: #f78989;
  color: #fff;
}

.action-btn.add-btn {
  background: #67c23a;
  color: #fff;
  border-color: #67c23a;
}

.action-btn.add-btn:hover {
  background: #85ce61;
  border-color: #85ce61;
  color: #fff;
}

.action-btn.export-btn {
  background: #409eff;
  color: #fff;
  border-color: #409eff;
}

.action-btn.export-btn:hover {
  background: #66b1ff;
  border-color: #66b1ff;
  color: #fff;
}

.action-btn.import-btn {
  background: #67c23a;
  color: #fff;
  border-color: #67c23a;
}

.action-btn.import-btn:hover {
  background: #85ce61;
  border-color: #85ce61;
  color: #fff;
}

.action-btn.push-btn {
  background: #e6a23c;
  color: #fff;
  border-color: #e6a23c;
}

.action-btn.push-btn:hover {
  background: #ebb563;
  border-color: #ebb563;
  color: #fff;
}

.action-btn.push-btn:disabled {
  background: #c0c4cc;
  border-color: #c0c4cc;
  color: #fff;
  cursor: not-allowed;
}

.action-btn.add-condition-btn,
.action-btn.remove-condition-btn {
  padding: 6px 10px;
  font-size: 12px;
  border-radius: 4px;
  border: 1px solid #dcdfe6;
  background: #fff;
  color: #606266;
  cursor: pointer;
  transition: all 0.3s;
}

.action-btn.add-condition-btn:hover,
.action-btn.remove-condition-btn:hover {
  background: #f5f7fa;
  border-color: #c0c4cc;
}
</style>
